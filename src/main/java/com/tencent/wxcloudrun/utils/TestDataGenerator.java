package com.tencent.wxcloudrun.utils;

import com.tencent.wxcloudrun.model.Addresses;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.api.IAddressesService;
import com.tencent.wxcloudrun.service.api.IWxUsersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 测试数据生成器
 * 用于生成测试用户、地址等假数据
 */
@Component
public class TestDataGenerator {

    private static final Logger log = LoggerFactory.getLogger(TestDataGenerator.class);

    @Autowired
    private IWxUsersService wxUsersService;

    @Autowired
    private IAddressesService addressesService;

    // 随机姓名池
    private static final String[] SURNAMES = {
        "张", "王", "李", "赵", "刘", "陈", "杨", "黄", "周", "吴",
        "徐", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗"
    };

    private static final String[] GIVEN_NAMES = {
        "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
        "勇", "艳", "杰", "娟", "涛", "明", "超", "秀", "霞", "平",
        "刚", "桂", "英", "华", "玉", "萍", "红", "娇", "辉", "鹏"
    };

    // 城市地址池
    private static final String[] PROVINCES = {
        "北京市", "上海市", "广东省", "浙江省", "江苏省", "四川省", "湖北省", "湖南省"
    };

    private static final String[] CITIES = {
        "北京市", "上海市", "广州市", "深圳市", "杭州市", "南京市", "成都市", "武汉市", "长沙市"
    };

    private static final String[] DISTRICTS = {
        "朝阳区", "海淀区", "西城区", "东城区", "丰台区", "石景山区", "通州区", "昌平区"
    };

    private static final String[] STREETS = {
        "中山路", "人民路", "解放路", "建设路", "新华路", "光明路", "胜利路", "和平路",
        "文化路", "学府路", "科技路", "创新路", "发展路", "繁华路", "商业街", "步行街"
    };

    /**
     * 随机生成测试用户
     * @param count 生成用户数量
     * @return 生成的用户列表
     */
    public List<WxUsers> generateTestUsers(int count) {
        List<WxUsers> users = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            try {
                WxUsers user = createRandomUser();
                wxUsersService.save(user);
                users.add(user);
                log.info("成功生成测试用户：openid={}, nickname={}, mobile={}", 
                    user.getOpenid(), user.getNickname(), user.getMobile());
            } catch (Exception e) {
                log.error("生成测试用户失败", e);
            }
        }
        
        return users;
    }

    /**
     * 为指定用户生成随机地址信息
     * @param users 用户列表
     * @return 生成的地址列表
     */
    public List<Addresses> generateAddressesForUsers(List<WxUsers> users) {
        List<Addresses> addresses = new ArrayList<>();
        
        for (WxUsers user : users) {
            try {
                // 为每个用户生成1-2个地址
                int addressCount = ThreadLocalRandom.current().nextInt(1, 3);
                for (int i = 0; i < addressCount; i++) {
                    Addresses address = createRandomAddress(user.getOpenid(), i == 0);
                    addressesService.save(address);
                    addresses.add(address);
                    log.info("成功为用户 {} 生成地址：{}", user.getOpenid(), address.getFullAddress());
                }
            } catch (Exception e) {
                log.error("为用户 {} 生成地址失败", user.getOpenid(), e);
            }
        }
        
        return addresses;
    }

    /**
     * 获取所有测试用户
     * @return 测试用户列表
     */
    public List<WxUsers> getTestUsers() {
        return wxUsersService.lambdaQuery()
            .like(WxUsers::getOpenid, "test_user_")
            .list();
    }

    /**
     * 获取随机测试用户的openid
     * @return 随机用户openid，如果没有测试用户则返回null
     */
    public String getRandomTestUserOpenId() {
        List<WxUsers> testUsers = getTestUsers();
        if (testUsers.isEmpty()) {
            return null;
        }
        return testUsers.get(ThreadLocalRandom.current().nextInt(testUsers.size())).getOpenid();
    }

    /**
     * 获取指定用户的随机地址ID
     * @param userId 用户openid
     * @return 随机地址ID，如果用户没有地址则返回null
     */
    public Long getRandomAddressIdForUser(String userId) {
        List<Addresses> userAddresses = addressesService.lambdaQuery()
            .eq(Addresses::getUserId, userId)
            .list();
        
        if (userAddresses.isEmpty()) {
            return null;
        }
        
        return userAddresses.get(ThreadLocalRandom.current().nextInt(userAddresses.size())).getId();
    }

    /**
     * 创建随机用户
     */
    private WxUsers createRandomUser() {
        WxUsers user = new WxUsers();

        long timestamp = System.currentTimeMillis();
        String uniqueId = String.valueOf(ShortSnowflakeId.generate());

        // 设置openid（作为用户唯一标识）
        user.setOpenid("test_user_" + uniqueId);

        // 生成随机姓名
        String surname = SURNAMES[ThreadLocalRandom.current().nextInt(SURNAMES.length)];
        String givenName = GIVEN_NAMES[ThreadLocalRandom.current().nextInt(GIVEN_NAMES.length)];
        String realName = surname + givenName;
        user.setRealName(realName);
        user.setNickname(realName);

        // 生成随机手机号
        user.setMobile("138" + String.format("%08d", ThreadLocalRandom.current().nextInt(10000000, 99999999)));

        // 设置性别
        user.setGender(ThreadLocalRandom.current().nextBoolean() ? "男" : "女");

        // 设置默认头像
        user.setCustomUrl("https://prod-7g0sy825f9a0d50e-1258823934.tcloudbaseapp.com/miniprogram/asset/images/avatar.jpg");

        // 设置其他字段
        user.setIsCertified(0);
        user.setServiceType(0);
        user.setRating(46L);
        user.setServiceCount(0L);
        user.setInviteCode(user.getOpenid());
        user.setCreateTime(timestamp);
        user.setUpdateTime(timestamp);

        return user;
    }

    /**
     * 为用户创建随机地址
     */
    private Addresses createRandomAddress(String userId, boolean isDefault) {
        Addresses address = new Addresses();

        // 设置用户ID
        address.setUserId(userId);

        // 设置地址别名
        address.setAlias(isDefault ? "家" : "公司");

        // 设置地址类型（1表示用户地址）
        address.setAddressesType("1");

        // 随机选择省市区
        String province = PROVINCES[ThreadLocalRandom.current().nextInt(PROVINCES.length)];
        String city = CITIES[ThreadLocalRandom.current().nextInt(CITIES.length)];
        String district = DISTRICTS[ThreadLocalRandom.current().nextInt(DISTRICTS.length)];
        String street = STREETS[ThreadLocalRandom.current().nextInt(STREETS.length)];

        address.setProvince(province);
        address.setCity(city);
        address.setDistrict(district);

        // 生成详细地址
        int buildingNumber = ThreadLocalRandom.current().nextInt(1, 200);
        int roomNumber = ThreadLocalRandom.current().nextInt(101, 2999);
        String fullAddress = province + city + district + street + buildingNumber + "号" + roomNumber + "室";
        address.setFullAddress(fullAddress);

        // 设置联系人信息
        address.setContactName("测试用户");
        address.setContactPhone("138" + String.format("%08d", ThreadLocalRandom.current().nextInt(10000000, 99999999)));

        // 生成随机坐标（北京地区范围）
        double baseLat = 39.9042;
        double baseLng = 116.4074;
        double latOffset = (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.1;
        double lngOffset = (ThreadLocalRandom.current().nextDouble() - 0.5) * 0.1;

        address.setLatitude(BigDecimal.valueOf(baseLat + latOffset));
        address.setLongitude(BigDecimal.valueOf(baseLng + lngOffset));

        // 设置是否默认地址
        address.setIsDefault(isDefault);

        // 设置时间戳
        long currentTime = System.currentTimeMillis();
        address.setCreateTime(currentTime);
        address.setUpdateTime(currentTime);

        return address;
    }
}
