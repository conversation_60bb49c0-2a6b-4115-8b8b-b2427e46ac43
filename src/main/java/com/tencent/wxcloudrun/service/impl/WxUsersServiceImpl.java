package com.tencent.wxcloudrun.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.config.BusinessException;
import com.tencent.wxcloudrun.dto.WxMiniProgramLoginRequest;
import com.tencent.wxcloudrun.enums.ResponseEnum;
import com.tencent.wxcloudrun.model.WxSessionCode;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.dao.WxUsersMapper;
import com.tencent.wxcloudrun.service.api.IWxSessionCodeService;
import com.tencent.wxcloudrun.service.api.IWxUsersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencent.wxcloudrun.utils.MD5PasswordExample;
import com.tencent.wxcloudrun.utils.UniqueGenerator;
import com.tencent.wxcloudrun.vo.AdminUserVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.tencent.wxcloudrun.enums.ResponseEnum.*;

/**
 * <p>
 * 用户基础信息与微信数据融合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
@Slf4j
@Service
public class WxUsersServiceImpl extends ServiceImpl<WxUsersMapper, WxUsers> implements IWxUsersService {

    @Resource
    private HttpServletRequest request;
    @Resource
    private WxUsersMapper wxUsersMapper;
    @Autowired
    private WxMaService wxMaService;
    @Value("${wx.appid}")
    private String APPID;
    @Value("${wx.secret}")
    private String SECRET;

    @Autowired
    private IWxSessionCodeService wxSessionCodeService;

    @Override
    public WxMaJscode2SessionResult getUserOpenId(WxMiniProgramLoginRequest.LoginCode loginRequest) {
        if (StrUtil.isEmpty(loginRequest.getCode())) {
            throw new BusinessException(LOGIN_ERROR.getCode(), "登录凭证code不能为空");
        }
        try {
            // 使用code换取openid和session_key
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(loginRequest.getCode());
            String openid = sessionInfo.getOpenid();
            String sessionKey = sessionInfo.getSessionKey();
            String unionid = sessionInfo.getUnionid();
            log.info("微信小程序登录，openid: {}, sessionKey: {}", openid, sessionKey != null ? "***" : null);
            if (StrUtil.isEmpty(openid)) {
                throw new BusinessException(LOGIN_ERROR.getCode(), "获取用户openid失败");
            }
            WxSessionCode wxSessionCode = wxSessionCodeService.openid2UserSessionId(openid);
            if (wxSessionCode == null) {
                wxSessionCode = WxSessionCode.builder()
                        .openid(openid)
                        .unionid(unionid)
                        .sessionKey(sessionKey)
                        .createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis())
                        .installTime(System.currentTimeMillis())
                        .build();
                wxSessionCodeService.save(wxSessionCode);
            } else {
                wxSessionCode.setOpenid(openid);
                wxSessionCode.setUnionid(unionid);
                wxSessionCode.setSessionKey(sessionKey);
                wxSessionCode.setUpdateTime(System.currentTimeMillis());
                wxSessionCode.setInstallTime(System.currentTimeMillis());
                wxSessionCodeService.updateById(wxSessionCode);
            }
            return sessionInfo;
        } catch (WxErrorException e) {
            log.error("微信小程序登录失败，code: {}, error: {}", loginRequest.getCode(), e.getMessage(), e);
            throw new BusinessException(LOGIN_ERROR.getCode(), "微信登录失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("微信小程序登录异常，code: {}, error: {}", loginRequest.getCode(), e.getMessage(), e);
            throw new BusinessException(LOGIN_ERROR.getCode(), "登录异常: " + e.getMessage());
        }
    }

    @Override
    public WxMaPhoneNumberInfo getUserPhone(WxMiniProgramLoginRequest.LoginCode loginRequest) {
        if (StrUtil.isEmpty(loginRequest.getCode())) {
            throw new BusinessException(LOGIN_ERROR.getCode(), "code不能为空");
        }
        try {
            WxMaPhoneNumberInfo phoneNumber = wxMaService.getUserService().getPhoneNumber(loginRequest.getCode());
            String phoneNumber1 = phoneNumber.getPhoneNumber();
            log.info("用户手机号，phoneNumber: {}", phoneNumber1);
            return phoneNumber;
        } catch (WxErrorException e) {
            log.error("微信小程序获取手机号失败，code: {}, error: {}", loginRequest.getCode(), e.getMessage(), e);
            throw new BusinessException(LOGIN_ERROR.getCode(), "微信小程序获取手机号异常: " + e.getMessage());
        } catch (Exception e) {
            log.error("微信小程序获取手机号异常，code: {}, error: {}", loginRequest.getCode(), e.getMessage(), e);
            throw new BusinessException(LOGIN_ERROR.getCode(), "微信小程序获取手机号异常: " + e.getMessage());
        }
    }



    @Override
    @Transactional
    public WxUsers miniProgramLogin(WxMiniProgramLoginRequest.loginUser loginRequest) {
        if (StrUtil.isEmpty(loginRequest.getOpenId())) {
            throw new BusinessException(OPENID_ERROR);
        }
        if (StrUtil.isEmpty(loginRequest.getMobile())) {
            throw new BusinessException(USER_MOBILE_REQUIRED);
        }
        // 根据openid查询用户
        WxUsers existUser = this.lambdaQuery().eq(WxUsers::getOpenid, loginRequest.getOpenId()).one();
        if (existUser == null) {
            // 第一次登录，创建新用户
            existUser = new WxUsers();
            existUser.setOpenid(loginRequest.getOpenId());
            // 设置用户信息
            if (StrUtil.isNotEmpty(loginRequest.getNickname())) {
                existUser.setNickname(loginRequest.getNickname());
            } else {
                existUser.setNickname(UniqueGenerator.generateUniqueNickname());
            }
            if (StrUtil.isNotEmpty(loginRequest.getAvatarUrl())) {
                existUser.setCustomUrl(loginRequest.getAvatarUrl());
            } else {
                existUser.setCustomUrl("https://prod-7g0sy825f9a0d50e-1258823934.tcloudbaseapp.com/miniprogram/asset/images/avatar.jpg");
            }

            if (StrUtil.isNotEmpty(loginRequest.getMobile())) {
                existUser.setMobile(loginRequest.getMobile());
            }
            if (StrUtil.isNotEmpty(loginRequest.getInviteCode())) {
                existUser.setWasInviteCode(loginRequest.getInviteCode());
            }

            existUser.setInviteCode(loginRequest.getOpenId()); // 使用openid作为邀请码
            existUser.setCreateTime(new Date().getTime());
            existUser.setUpdateTime(new Date().getTime());
            // 保存新用户
            wxUsersMapper.insert(existUser);
            log.info("创建新用户成功，openid: {}, id: {}", loginRequest.getOpenId(), existUser.getId());
        } else {
        if (StrUtil.isEmpty(existUser.getMobile())) {
            existUser.setMobile(loginRequest.getMobile());
        }
        if (!existUser.getMobile().equals(loginRequest.getMobile())) {
            existUser.setInviteCode(loginRequest.getMobile());
        }
        wxUsersMapper.updateById(existUser);
    }

        return existUser;
    }


    /**
     * 用户登录
     *
     * @return
     */
    @Override
    @Transactional
    public WxUsers login(WxUsers wxUser) {
        if (StrUtil.isEmpty(wxUser.getOpenid())) {
            throw new BusinessException(LOGIN_ERROR);
        }
        String openId = request.getHeader("X-WX-OPENID");
        if (!openId.equals(wxUser.getOpenid())) {
            throw new BusinessException(USER_INFO_NOT_EXIST);
        }

        WxUsers wxUsers = this.lambdaQuery().eq(WxUsers::getOpenid, openId).orderByAsc(WxUsers::getId).one();
        if (wxUsers == null) {
            // 第一次登录创建登录用户
            if (!StrUtil.isEmpty(wxUser.getUserPassword())) {
                String password = MD5PasswordExample.encryptMD5(wxUser.getUserPassword());
                wxUser.setUserPassword(password);
            }
            createWxUser(wxUser);
        } else {
            // 判断用户是否输入密码
            if (!StrUtil.isEmpty(wxUser.getMobile()) && !StrUtil.isEmpty(wxUser.getUserPassword()) && !StrUtil.isEmpty(wxUsers.getUserPassword())) {
                boolean passwordFlag = MD5PasswordExample.verifyPassword(wxUser.getUserPassword(), wxUsers.getUserPassword());
                if (!passwordFlag) {
                    throw new BusinessException(PASSWORD_ERROR);
                }
            }
            // 判断用户是否输入手机号
            if (!StrUtil.isEmpty(wxUser.getMobile()) && !StrUtil.isEmpty(wxUsers.getMobile()) && !wxUsers.getMobile().equals(wxUser.getMobile())) {
                throw new BusinessException(MOBILE_ERROR);

            }
            // 更新用户手机号和密码
            if (StrUtil.isEmpty(wxUsers.getMobile()) || StrUtil.isEmpty(wxUsers.getUserPassword())) {
                if (!StrUtil.isEmpty(wxUser.getMobile())) {
                    wxUsers.setMobile(wxUser.getMobile());
                }
                if (!StrUtil.isEmpty(wxUser.getUserPassword())) {
                    String password = MD5PasswordExample.encryptMD5(wxUser.getUserPassword());
                    wxUsers.setUserPassword(password);
                }
                this.updateUser(wxUsers);
            }
            BeanUtil.copyProperties(wxUsers, wxUser);
        }

        return wxUser;
    }


    @Override
    public int updateUser(WxUsers wxUser) {
        if (wxUser == null || wxUser.getId() == null) {
            throw new BusinessException(ResponseEnum.LOGIN_ERROR);
        }
        if ((StrUtil.isNotEmpty(wxUser.getIdentityId()) || StrUtil.isNotEmpty(wxUser.getIdentityUrl()))
                && (wxUser.getIsCertified() == null || wxUser.getIsCertified() == 0)) {
            wxUser.setIsCertified(1);
        }
        return wxUsersMapper.updateById(wxUser);
    }

    @Override
    public List<WxUsers> getByOpenIds(List<String> openIds) {
        if (CollectionUtils.isEmpty(openIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WxUsers> queryWrapper = new LambdaQueryWrapper<>();
        // 添加 IN 查询条件
        queryWrapper.in(WxUsers::getOpenid, openIds);
        // 执行查询
        return wxUsersMapper.selectList(queryWrapper);
    }

    @Override
    public WxUsers getByMobile(String mobile) {
        List<WxUsers> list = this.lambdaQuery().eq(WxUsers::getMobile, mobile).list();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据手机号模糊查询
     *
     * @param mobile 手机号
     * @return List<WxUsers>
     */
    @Override
    public List<WxUsers> getByLikeMobile(String mobile) {

        return this.lambdaQuery().like(WxUsers::getMobile, mobile).list();

    }

    /**
     * 根据用户昵称模糊查询
     *
     * @param nickName 用户昵称
     * @return List<WxUsers>
     */
    @Override
    public List<WxUsers> getByLikeNikeName(String nickName) {
        return this.lambdaQuery().like(WxUsers::getNickname, nickName).list();
    }

    @Override
    public Page<WxUsers> scan(int serviceType, int pageSize, long maxId) {
        // 1. 参数安全校验（避免非法参数导致全表扫描）
        int safePageNum = 1;          // 永远都是第一页
        int safePageSize = Math.min(pageSize, 1000);     // 限制每页最多1000条

        // 2. 构建分页对象
        Page<WxUsers> page = new Page<>(safePageNum, safePageSize);

        // 3. 设置排序规则（倒序按ID排序，优先用时间字段如 createTime）
        page.addOrder(OrderItem.asc("id"));

        // 4. 构建查询条件
        LambdaQueryWrapper<WxUsers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(WxUsers::getServiceType, serviceType)
                .eq(WxUsers::getIsCertified, true)
                .lt(WxUsers::getId, maxId);        // 小于符号

        // 5. 执行分页查询
        return this.page(page, queryWrapper);           // 注意用 this.page() 方法
    }

    @Override
    public Page<WxUsers> userListPage(AdminUserVO.UserPage userListPage) {
        // 2. 构建分页对象
        Page<WxUsers> page = new Page<>(userListPage.getCurrent(), userListPage.getSize());
        // 3. 设置排序规则（倒序按ID排序，优先用时间字段如 createTime）
        page.addOrder(OrderItem.asc("id"));

        // 4. 构建查询条件
        LambdaQueryWrapper<WxUsers> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotEmpty(userListPage.getUserId())) {
            // 按照用户ID过滤
            queryWrapper.like(WxUsers::getOpenid, userListPage.getUserId());
        }
        if (StrUtil.isNotEmpty(userListPage.getNickName())) {
            // 按照用户名称
            queryWrapper.like(WxUsers::getNickname, userListPage.getNickName());
        }
        if (CollectionUtil.isNotEmpty(userListPage.getUserIdList())) {
            // 按照用户ID list过滤
            queryWrapper.in(WxUsers::getOpenid, userListPage.getUserIdList());
        }
        if (StrUtil.isNotEmpty(userListPage.getMobile())) {
            // 按照用户手机号过滤
            queryWrapper.like(WxUsers::getMobile, userListPage.getMobile());
        }
        // 5. 执行分页查询
        return this.page(page, queryWrapper);
    }

    @Override
    public boolean updatePhone(WxMiniProgramLoginRequest.PhoneCode phoneCode) {


        return false;
    }

    /**
     * 创建用户
     *
     * @param wxUser 返回的用户信息
     */
    private void createWxUser(WxUsers wxUser) {

        WxUsers users = new WxUsers();
        // 设置属性
        String nickname = UniqueGenerator.generateUniqueNickname();
        if (StrUtil.isEmpty(users.getNickname())) {
            users.setNickname(nickname);
        }
        if (StrUtil.isEmpty(users.getCustomUrl())) {
            // 固定值
            users.setCustomUrl("https://prod-7g0sy825f9a0d50e-1258823934.tcloudbaseapp.com/miniprogram/asset/images/avatar.jpg");
        }
        users.setOpenid(wxUser.getOpenid());
        users.setCreateTime(new Date().getTime());
        users.setUpdateTime(new Date().getTime());
        users.setInviteCode(wxUser.getOpenid());
        users.setMobile(wxUser.getMobile());
        users.setUserPassword(wxUser.getUserPassword());
        wxUsersMapper.insert(users);
        BeanUtils.copyProperties(users, wxUser);
    }


}
