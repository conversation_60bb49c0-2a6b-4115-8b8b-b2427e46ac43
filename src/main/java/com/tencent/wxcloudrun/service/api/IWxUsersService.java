package com.tencent.wxcloudrun.service.api;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tencent.wxcloudrun.dto.WxMiniProgramLoginRequest;
import com.tencent.wxcloudrun.dto.WxUserRequest;
import com.tencent.wxcloudrun.model.WxUsers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tencent.wxcloudrun.vo.AdminUserVO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 用户基础信息与微信数据融合表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-01
 */
public interface IWxUsersService extends IService<WxUsers> {

    WxUsers login(WxUsers wxUser);


    WxMaJscode2SessionResult getUserOpenId(WxMiniProgramLoginRequest.LoginCode loginRequest);


    WxMaPhoneNumberInfo getUserPhone(WxMiniProgramLoginRequest.LoginCode loginRequest);

    /**
     * 微信小程序登录
     * @param loginRequest 登录请求参数
     * @return 用户信息
     */
    WxUsers miniProgramLogin(WxMiniProgramLoginRequest.loginUser loginRequest);

    int updateUser(WxUsers wxUser);

    List<WxUsers> getByOpenIds(List<String> openIds);

    default WxUsers getByOpenId(String openId) {
        List<WxUsers> wxUsers = getByOpenIds(Collections.singletonList(openId));
        return CollectionUtils.isEmpty(wxUsers) ? null : wxUsers.get(0);
    }
    WxUsers getByMobile(String mobile);
    List<WxUsers> getByLikeMobile(String mobile);

    List<WxUsers> getByLikeNikeName(String nickName);

    Page<WxUsers> scan(int serviceType, int pageSize, long maxId);

    Page<WxUsers> userListPage(AdminUserVO.UserPage userListPage);

    boolean updatePhone(WxMiniProgramLoginRequest.PhoneCode phoneCode);
}
