package com.tencent.wxcloudrun.task;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tencent.wxcloudrun.enums.OrderStatusEnum;
import com.tencent.wxcloudrun.enums.ServiceTypeEnum;
import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.service.api.AdminOrderService;
import com.tencent.wxcloudrun.service.api.IOrdersService;
import com.tencent.wxcloudrun.service.api.UserOrderService;
import com.tencent.wxcloudrun.service.api.UserWalletService;
import com.tencent.wxcloudrun.utils.ShortSnowflakeId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;


@Component
public class ScheduledTasks {

    private static final Logger log = LoggerFactory.getLogger(ScheduledTasks.class);
    @Autowired
    private UserOrderService userOrderService;
    @Autowired
    private AdminOrderService adminOrderService;
    @Autowired
    private IOrdersService ordersService;

    // 每天上午8点执行（默认时区）
    @Scheduled(cron = "0 0 10 * * ?")
    //@Scheduled(cron = "0 * * * * ?")
    public void dailyTask() {
        log.info("每日任务执行：{}", new Date());
        List<Orders> timeAndStartOrders = userOrderService.getTimeAndStartOrders(1);
        log.info("每日任务执行,获取订单数据：{}", timeAndStartOrders);

        // 判断timeAndStartOrders 是否为空 是空之间return  非空进行变量操作
        if (Objects.isNull(timeAndStartOrders) || timeAndStartOrders.isEmpty()) {
            return;
        }
        for (Orders orders : timeAndStartOrders) {
            adminOrderService.orderUnfreeze(orders.getOrderNo());
        }
        log.info("每日任务执行：{}", new Date());

    }

    /**
     * 自动创建订单任务 - 每天随机创建1-3个订单
     * 每天上午9点执行
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void autoCreateOrdersTask() {
        log.info("自动创建订单任务开始执行：{}", new Date());

        try {
            // 随机生成1-3个订单
            int orderCount = ThreadLocalRandom.current().nextInt(1, 4);
            log.info("今日将创建 {} 个订单", orderCount);

            for (int i = 0; i < orderCount; i++) {
                createRandomOrder();
            }

            log.info("自动创建订单任务完成，共创建 {} 个订单", orderCount);
        } catch (Exception e) {
            log.error("自动创建订单任务执行失败", e);
        }
    }

    /**
     * 创建一个随机订单
     */
    private void createRandomOrder() {
        try {
            // 创建订单对象
            Orders order = new Orders();

            // 生成订单号
            order.setOrderNo(String.valueOf(ShortSnowflakeId.generate()));

            // 设置订单状态为已支付待接单
            order.setOrdersStatus(OrderStatusEnum.PENDING_ACCEPTANCE.getStatusCode());

            // 设置固定的接单用户
            order.setServiceUserId("158111111111");

            // 设置随机下单用户（使用测试用户ID）
            order.setUserId("test_user_" + System.currentTimeMillis());

            // 随机选择服务类型（猫或狗）
            ServiceTypeEnum[] serviceTypes = ServiceTypeEnum.values();
            ServiceTypeEnum randomServiceType = serviceTypes[ThreadLocalRandom.current().nextInt(serviceTypes.length)];
            order.setServiceType(randomServiceType.getServiceType());

            // 设置测试宠物ID和地址ID
            order.setPetId(1L); // 使用默认测试宠物ID
            order.setAddressId(1L); // 使用默认测试地址ID

            // 随机生成支付金额（30-50元，转换为分）
            long randomAmount = ThreadLocalRandom.current().nextLong(3000, 5001);
            order.setOriginalPrice(randomAmount);
            order.setFinalPrice(randomAmount);

            // 设置服务时间为明天
            LocalDate tomorrow = LocalDate.now().plusDays(1);
            String serviceTime = tomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            order.setServiceTime(JSONUtil.toJsonStr(Collections.singletonList(serviceTime)));

            // 设置其他必需字段
            order.setWxTransactionId("auto_" + System.currentTimeMillis());
            order.setServiceFlow("[]");
            order.setCouponIds("[]");
            order.setServiceCouponIds("[]");
            order.setRemarks("自动生成的测试订单");
            order.setHandover("密码交接");
            order.setHandoverType(1); // 1表示密码交接

            // 设置时间戳
            long currentTime = System.currentTimeMillis();
            order.setCreateTime(currentTime);
            order.setUpdateTime(currentTime);
            order.setPayTime(currentTime); // 设置为已支付

            // 设置删除标记
            order.setIsDelete(0);

            // 保存订单
            ordersService.save(order);

            log.info("成功创建自动订单：订单号={}, 金额={}, 服务类型={}, 接单用户={}",
                    order.getOrderNo(), randomAmount, randomServiceType.getDesc(), order.getServiceUserId());

        } catch (Exception e) {
            log.error("创建随机订单失败", e);
        }
    }

}