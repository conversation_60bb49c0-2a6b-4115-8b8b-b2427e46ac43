package com.tencent.wxcloudrun.task;

import cn.hutool.core.util.StrUtil;
import com.tencent.wxcloudrun.model.Orders;
import com.tencent.wxcloudrun.service.api.AdminOrderService;
import com.tencent.wxcloudrun.service.api.IOrdersService;
import com.tencent.wxcloudrun.service.api.UserOrderService;
import com.tencent.wxcloudrun.service.api.UserWalletService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Component
public class ScheduledTasks {

    private static final Logger log = LoggerFactory.getLogger(ScheduledTasks.class);
    @Autowired
    private UserOrderService userOrderService;
    @Autowired
    private AdminOrderService adminOrderService;
    @Autowired
    private IOrdersService ordersService;

    // 每天上午8点执行（默认时区）
    @Scheduled(cron = "0 0 10 * * ?")
    //@Scheduled(cron = "0 * * * * ?")
    public void dailyTask() {
        log.info("每日任务执行：{}", new Date());
        List<Orders> timeAndStartOrders = userOrderService.getTimeAndStartOrders(1);
        log.info("每日任务执行,获取订单数据：{}", timeAndStartOrders);

        // 判断timeAndStartOrders 是否为空 是空之间return  非空进行变量操作
        if (Objects.isNull(timeAndStartOrders) || timeAndStartOrders.isEmpty()) {
            return;
        }
        for (Orders orders : timeAndStartOrders) {
            adminOrderService.orderUnfreeze(orders.getOrderNo());
        }
        log.info("每日任务执行：{}", new Date());

    }



}