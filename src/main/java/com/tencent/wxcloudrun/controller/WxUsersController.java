package com.tencent.wxcloudrun.controller;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.util.StrUtil;
import com.tencent.wxcloudrun.config.ApiResponse;
import com.tencent.wxcloudrun.dto.WxMiniProgramLoginRequest;
import com.tencent.wxcloudrun.dto.WxUserRequest;
import com.tencent.wxcloudrun.model.WxUsers;
import com.tencent.wxcloudrun.service.api.IWxUsersService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.binarywang.wx.miniapp.api.WxMaService;

@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping("/api/WxUsers")
@Api(tags = "WxUsers管理接口", description = "提供WxUsers相关的增删改查服务")
public class WxUsersController {

    @Autowired
    private IWxUsersService service;


    @GetMapping("/id/{openId}")
    @ApiOperation(value = "获取WxUsers", notes = "获取WxUsers")
    public ApiResponse getObjectById(@PathVariable String openId) {
        return ApiResponse.ok(service.getByOpenId(openId));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新WxUsers", notes = "根据ID修改WxUsers记录")
    public ApiResponse update(@RequestBody @ApiParam(value = "WxUsers对象", required = true) WxUsers entity) {
        return ApiResponse.ok(service.updateUser(entity));
    }

    @PostMapping("/login")
    @ApiOperation(value = "微信登录", notes = "用户登录接口")
    public ApiResponse<WxUsers> miniProgramLogin(@RequestBody @ApiParam(value = "WxUsers对象", required = true) WxUsers wxUser) {

        WxUsers wxUserReq = service.login(wxUser);

        wxUserReq.setUserPassword("");
        return ApiResponse.ok(wxUserReq);
    }

    @PostMapping("/miniProgramLogin")
    @ApiOperation(value = "微信小程序登录", notes = "使用openId获取用户信息")
    public ApiResponse<WxUsers> miniProgramLogin(@RequestBody @ApiParam(value = "微信小程序登录请求", required = true) WxMiniProgramLoginRequest.loginUser loginRequest) {

        WxUsers wxUser = service.miniProgramLogin(loginRequest);
        // 清空密码字段，避免返回敏感信息
        wxUser.setUserPassword("");
        return ApiResponse.ok(wxUser);
    }

    @PostMapping("/getUserOpenId")
    @ApiOperation(value = "获取微信用户OpenId", notes = "使用微信小程序code进行登录")
    public ApiResponse<WxMaJscode2SessionResult> getUserOpenId(@RequestBody @ApiParam(value = "微信小程序登录请求", required = true) WxMiniProgramLoginRequest.LoginCode loginRequest) {
        WxMaJscode2SessionResult userOpenId = service.getUserOpenId(loginRequest);
        return ApiResponse.ok(userOpenId);
    }

    @PostMapping("/getUserPhone")
    @ApiOperation(value = "使用微信小程序code获取手机号", notes = "该接口用于将code换取用户手机号。 说明，每个code只能使用一次，code的有效期为5min。 ")
    public ApiResponse<WxMaPhoneNumberInfo> getUserPhone(@RequestBody @ApiParam(value = "使用微信小程序code获取手机号", required = true) WxMiniProgramLoginRequest.LoginCode loginRequest) {
        WxMaPhoneNumberInfo userPhone = service.getUserPhone(loginRequest);
        return ApiResponse.ok(userPhone);
    }

    @PostMapping("/updatePhone")
    @ApiOperation(value = "修改手机号", notes = "修改手机号")
    public ApiResponse updatePhone(@RequestBody @ApiParam(value = "使用微信小程序code获取手机号", required = true) WxMiniProgramLoginRequest.PhoneCode phoneCode) {

        return ApiResponse.ok(service.updatePhone(phoneCode));
    }
}