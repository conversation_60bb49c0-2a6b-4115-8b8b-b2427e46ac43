server:
  port: 8080
  ssl:
    key-store: classpath:ermaopet.cn.pfx
    key-store-type: PKCS12
    key-store-password: 596k49it4wx5g38
    key-alias: ermaopet.cn
wx:
  appid: wxe917114e201642c1 #微信小程序的appid
  env: prod-4glmr16t2e609766
  secret: e69f00e44f19721ef6d641a7a15c0089
  subMchId: 1713570828
  pay_ip: *************
  privateKeyPath: classpath:apiclient_key.pem
  merchantSerialNumber: 297D9BAE7682BAA198620C2C7B90CA761C5A9469
  apiV3key: maomaochongwukeji20250413FMCqian
  prod_env: test
#spring:
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: jdbc:mysql://${MYSQL_ADDRESS}/${MYSQL_DATABASE:springboot_demo}
#    username: ${MYSQL_USERNAME}
#    password: ${MySQL_PASSWORD}
TENCENT:
#  SECRET_ID: AKIDFLBl58vRggOq1way5vUm1FNMKBsRu9Jb
#  SECRET_KEY: OuJbeS0IKAk48wUmtjJd1MS8fxzlHXif
  SECRET_ID: AKIDV2MSPCm8HCuPmxukLiqmVzTN8nHcUBi2
  SECRET_KEY: CAuQhssgHdlfNs9C3i181h8RWN4YoqgI
  REGION: faceid.tencentcloudapi.com
  COS:
    REGION: ap-beijing
    BUCKET_NAME: maomao-1353640424
  SMS:
    SDK_APP_ID: 1401025028
    SIGN_NAME: 北京顶碗科技有限公司
    TEMPLATE_ID: 2497622
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************
    username: root
    password: <EMAIL>
  mvc:
    view:
      suffix: .html
      prefix: /
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
mybatis:
  mapper-locations: classpath*:mapper/*Mapper.xml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

logging:
  file:
    name: /app/logs/spring_boot_log.log
  level:
    name: INFO
    com.course.server.mapper: trace
  logback:
    rollingpolicy:
      max-history: 100
      max-file-size: 10MB