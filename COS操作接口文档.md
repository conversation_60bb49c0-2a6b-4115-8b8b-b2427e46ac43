# COS对象存储操作接口文档

## 概述

本文档详细说明了系统中腾讯云COS对象存储的操作接口，包括文件上传、下载、删除、URL生成等常用功能。

## 接口列表

### 1. 文件上传接口

#### 1.1 上传文件
- **接口地址**: `/api/External/cos/upload`
- **请求方法**: `POST`
- **请求类型**: `multipart/form-data`
- **功能说明**: 上传文件到腾讯云COS，支持指定文件夹路径
- **请求参数**:
  - `file` (MultipartFile, 必填): 要上传的文件
  - `folder` (String, 可选): 文件夹路径，如 `images/avatars/`
- **返回结果**:
  ```json
  {
    "code": 0,
    "data": {
      "fileName": "文件在COS中的路径和名称",
      "url": "文件访问URL"
    },
    "errMsg": "成功"
  }
  ```

#### 1.2 上传字节数组
- **功能说明**: 通过CosApiUtils工具类提供
- **调用方法**: `cosApiUtils.uploadFile(byte[] bytes, String fileName)`
- **参数说明**:
  - `bytes`: 文件字节数组
  - `fileName`: 文件名（包含路径）

### 2. 文件下载接口

#### 2.1 下载文件为响应体（在线查看）
- **接口地址**: `/api/External/cos/view`
- **请求方法**: `GET`
- **功能说明**: 在线查看COS中的文件，根据文件类型自动设置Content-Type
- **请求参数**:
  - `fileName` (String, 必填): COS中的文件名/Key
- **返回结果**: 文件内容直接作为响应体返回

#### 2.2 下载文件为附件
- **接口地址**: `/api/External/cos/download`
- **请求方法**: `GET`
- **功能说明**: 下载COS中的文件为附件
- **请求参数**:
  - `fileName` (String, 必填): COS中的文件名/Key
- **返回结果**: 文件以附件形式下载

#### 2.3 下载为字节数组
- **功能说明**: 通过CosApiUtils工具类提供
- **调用方法**: `cosApiUtils.downloadFileAsBytes(String fileName)`
- **参数说明**:
  - `fileName`: COS中的文件名/Key
- **返回结果**: 文件字节数组

### 3. URL生成接口

#### 3.1 获取文件访问URL
- **接口地址**: `/api/External/cos/imageUrl`
- **请求方法**: `GET`
- **功能说明**: 获取文件的访问URL，支持生成临时预签名URL
- **请求参数**:
  - `fileName` (String, 必填): COS中的文件名/Key
  - `presignSeconds` (Long, 可选): 预签名URL有效期（秒），不传则返回永久URL
- **返回结果**:
  ```json
  {
    "code": 0,
    "data": "https://bucket-name.cos.region.myqcloud.com/file-path",
    "errMsg": "成功"
  }
  ```

### 4. 文件删除接口

#### 4.1 删除单个文件
- **功能说明**: 通过CosApiUtils工具类提供
- **调用方法**: `cosApiUtils.deleteFile(String fileName)`
- **参数说明**:
  - `fileName`: 要删除的文件名
- **返回结果**: boolean，删除成功返回true

#### 4.2 批量删除文件
- **功能说明**: 通过CosApiUtils工具类提供
- **调用方法**: `cosApiUtils.batchDeleteFiles(String[] fileNames)`
- **参数说明**:
  - `fileNames`: 要删除的文件名数组
- **返回结果**: DeleteObjectsResult，删除结果对象

### 5. 文件信息查询接口

#### 5.1 检查文件是否存在
- **功能说明**: 通过CosApiUtils工具类提供
- **调用方法**: `cosApiUtils.doesFileExist(String fileName)`
- **参数说明**:
  - `fileName`: 文件名
- **返回结果**: boolean，存在返回true

#### 5.2 获取文件元数据
- **功能说明**: 通过CosApiUtils工具类提供
- **调用方法**: `cosApiUtils.getFileMetadata(String fileName)`
- **参数说明**:
  - `fileName`: 文件名
- **返回结果**: ObjectMetadata，文件元数据信息

## 使用示例

### 1. 上传文件示例
```bash
curl -X POST "http://localhost:8080/api/External/cos/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/file.jpg" \
  -F "folder=images/"
```

### 2. 查看文件示例
```bash
curl -X GET "http://localhost:8080/api/External/cos/view?fileName=images/abc123.jpg" \
  -H "Accept: image/jpeg"
```

### 3. 下载文件示例
```bash
curl -X GET "http://localhost:8080/api/External/cos/download?fileName=images/abc123.jpg" \
  -O -J
```

### 4. 获取文件URL示例
```bash
# 获取永久URL
curl -X GET "http://localhost:8080/api/External/cos/imageUrl?fileName=images/abc123.jpg"

# 获取1小时有效期的预签名URL
curl -X GET "http://localhost:8080/api/External/cos/imageUrl?fileName=images/abc123.jpg&presignSeconds=3600"
```

## 注意事项

1. 所有文件操作接口都需要正确配置腾讯云COS的密钥信息
2. 上传文件时，如果不指定文件名，系统会自动生成唯一文件名
3. 预签名URL适用于私有文件的临时访问授权
4. 大文件上传建议在前端实现分片上传
5. 所有文件操作都有完整的日志记录，便于问题排查