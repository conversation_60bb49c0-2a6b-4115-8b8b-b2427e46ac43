# 文件上传配置说明

## 问题描述

系统出现以下错误：
```
"系统内部错误: Maximum upload size exceeded; nested exception is java.lang.IllegalStateException: org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 1048576 bytes."
```

这是由于Spring Boot默认的文件上传大小限制为1MB，当上传的文件超过这个限制时就会报错。

## 解决方案

已在配置文件中添加了文件上传大小限制的配置：

### 1. application.yml (主配置文件)

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 50MB      # 单个文件最大大小
      max-request-size: 50MB   # 整个请求最大大小
```

### 2. application_test.yml (测试环境配置文件)

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 50MB      # 单个文件最大大小
      max-request-size: 50MB   # 整个请求最大大小
```

## 配置说明

- `max-file-size`: 设置单个文件的最大大小，这里设置为50MB
- `max-request-size`: 设置整个multipart请求的最大大小，这里也设置为50MB

## 可选配置值

根据实际需求，可以调整为以下值：
- 10MB: 适用于一般图片和文档上传
- 50MB: 适用于高清图片和小型视频上传
- 100MB: 适用于大型文件上传
- -1: 无限制（不推荐，可能造成内存溢出）

## 生效方式

修改配置后，需要重启应用才能使配置生效。

## 注意事项

1. 根据实际业务需求合理设置文件大小限制
2. 大文件上传建议考虑分片上传方案
3. 需要在所有环境配置文件中保持一致的配置
4. 生产环境建议设置合理的限制，避免资源被恶意占用